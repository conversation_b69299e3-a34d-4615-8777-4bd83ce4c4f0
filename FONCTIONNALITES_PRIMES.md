# Fonctionnalités de Gestion des Primes - Sélection de Fonction et Coefficients

## Vue d'ensemble

Ce document décrit les nouvelles fonctionnalités implémentées pour la gestion des primes, permettant la sélection de fonction avec changement automatique de coefficient et validation pour persister en base de données.

## Fonctionnalités Implémentées

### 1. Système de Coefficients par Fonction et Type de Prime

#### Fichiers créés/modifiés :
- `erp-frontend/src/models/Agent.ts` - Ajout des interfaces `Fonction` et `CoefficientConfig`
- `erp-frontend/src/mocks/fixtures/fonctions.ts` - Configuration des fonctions et coefficients
- `erp-frontend/src/services/api/fonctionService.ts` - Service pour gérer les fonctions et coefficients

#### Configuration des coefficients :
Les coefficients varient selon la fonction et le type de prime :

**Fonctions disponibles :**
- Chef de service
- Responsable d'unité
- Agent de terrain
- Vérificateur
- Directeur adjoint
- Contrôleur
- Inspecteur
- Secrétaire
- Chauffeur

**Types de primes :**
- Prime Annuelle
- Prime de Performance
- Prime de Projet
- Prime Exceptionnelle
- Autre Prime

**Exemple de coefficients :**
- Chef de service + Prime Annuelle = 1.5
- Chef de service + Prime de Performance = 2.0
- Agent de terrain + Prime Annuelle = 1.0
- Agent de terrain + Prime de Performance = 1.2

### 2. Sélection de Fonction avec Changement Automatique de Coefficient

#### Dans `AddRepartitionPage.tsx` :

**Colonne Fonction transformée en Select :**
```typescript
{
  title: "Fonction",
  dataIndex: "fonction",
  key: "fonction",
  render: (fonction: string, record: Agent) => {
    return (
      <Select
        value={fonction}
        onChange={(value) => handleFonctionChange(record.id, value)}
        style={{ width: '100%' }}
        placeholder="Sélectionner une fonction"
      >
        {fonctions.map(f => (
          <Option key={f.code} value={f.code}>
            {f.libelle}
          </Option>
        ))}
      </Select>
    );
  }
}
```

**Gestion du changement de fonction :**
```typescript
const handleFonctionChange = (agentId: string, nouvelleFonction: string) => {
  if (!selectedType) return;

  const nouveauCoefficient = getCoefficientByFonctionAndPrimeType(nouvelleFonction, selectedType.code as any);
  
  setAgentChanges(prev => ({
    ...prev,
    [agentId]: {
      fonction: nouvelleFonction,
      coefficient: nouveauCoefficient
    }
  }));

  // Mettre à jour l'agent dans la liste locale pour l'affichage
  setAgents(prev => prev.map(agent => 
    agent.id === agentId 
      ? { ...agent, fonction: nouvelleFonction, coefficient: nouveauCoefficient }
      : agent
  ));
};
```

### 3. Affichage du Coefficient avec Indication de Modification

**Colonne Coefficient mise à jour :**
```typescript
{
  title: "Coefficient",
  dataIndex: "coefficient",
  key: "coefficient",
  render: (coef: number, record: Agent) => {
    const hasChanges = agentChanges[record.id];
    const displayCoef = hasChanges ? hasChanges.coefficient : coef;
    
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <InputNumber
          value={displayCoef}
          min={0}
          step={0.1}
          style={{ width: '100px' }}
          disabled
        />
        {hasChanges && (
          <Typography.Text type="success" style={{ fontSize: '12px' }}>
            Modifié
          </Typography.Text>
        )}
      </div>
    );
  }
}
```

### 4. Bouton de Validation pour Persister les Changements

**Zone de notification des changements :**
```typescript
{Object.keys(agentChanges).length > 0 && (
  <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
    <Typography.Text strong style={{ color: '#52c41a' }}>
      {Object.keys(agentChanges).length} agent(s) modifié(s)
    </Typography.Text>
    <Button
      type="primary"
      icon={<CheckOutlined />}
      onClick={handleValidateChanges}
      loading={validating}
      style={{ marginLeft: 16 }}
      size="small"
    >
      Valider les changements
    </Button>
  </div>
)}
```

**Fonction de validation :**
```typescript
const handleValidateChanges = async () => {
  if (Object.keys(agentChanges).length === 0) {
    message.warning("Aucun changement à valider");
    return;
  }

  setValidating(true);
  try {
    // Mettre à jour chaque agent modifié
    const updatePromises = Object.entries(agentChanges).map(([agentId, changes]) =>
      updateAgent(agentId, {
        fonction: changes.fonction,
        coefficient: changes.coefficient
      })
    );

    await Promise.all(updatePromises);
    message.success(`${Object.keys(agentChanges).length} agent(s) mis à jour avec succès`);
    setAgentChanges({}); // Réinitialiser les changements
  } catch (error) {
    message.error("Erreur lors de la mise à jour des agents");
  } finally {
    setValidating(false);
  }
};
```

### 5. Service de Mise à Jour des Agents

**Dans `agentService.ts` :**
```typescript
export const updateAgent = async (id: string, agentData: Partial<Agent>): Promise<Agent> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // const response = await request.put(`${API_URL}/${id}`, { data: agentData });
    // return response.data;

    // Pour le moment, on simule juste un délai et on retourne l'agent modifié
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Simuler la mise à jour de l'agent dans les données mockées
    const agent = agents.find(a => a.id === id);
    if (!agent) {
      throw new Error(`Agent with id ${id} not found`);
    }
    
    const updatedAgent = { ...agent, ...agentData };
    const index = agents.findIndex(a => a.id === id);
    if (index !== -1) {
      agents[index] = updatedAgent;
    }
    
    return Promise.resolve(updatedAgent);
  } catch (error) {
    console.error(`Error updating agent with id ${id}:`, error);
    throw error;
  }
};
```

## Flux d'Utilisation

1. **Sélection du type de prime** : L'utilisateur sélectionne un type de prime dans la première étape
2. **Affichage des agents** : Les agents sont chargés avec leurs fonctions et coefficients actuels
3. **Modification de fonction** : L'utilisateur peut changer la fonction d'un agent via le Select
4. **Calcul automatique du coefficient** : Le coefficient est automatiquement mis à jour selon la fonction et le type de prime
5. **Indication visuelle** : Les agents modifiés sont marqués avec "Modifié" et une zone de notification apparaît
6. **Validation** : L'utilisateur clique sur "Valider les changements" pour persister en base
7. **Confirmation** : Un message de succès confirme la mise à jour

## Avantages

- **Automatisation** : Les coefficients sont calculés automatiquement selon les règles métier
- **Traçabilité** : Les modifications sont clairement indiquées avant validation
- **Flexibilité** : Possibilité de modifier plusieurs agents avant de valider
- **Sécurité** : Validation explicite requise pour persister les changements
- **UX optimisée** : Interface intuitive avec feedback visuel immédiat

## Configuration

Pour ajouter de nouvelles fonctions ou modifier les coefficients, il suffit de mettre à jour le fichier `erp-frontend/src/mocks/fixtures/fonctions.ts`.

Les coefficients peuvent être ajustés selon les besoins métier en modifiant le tableau `coefficientsConfig`.
