import React from "react"
import { create<PERSON><PERSON>erRouter, Navigate, RouteObject } from "react-router"
import { BASENAME, PATH_ADMIN, PATH_AUTH, PATH_GUEST, REQUEST, SIGNIN, UPDATE_PASSWORD } from "./constants"
import { loadPage, wrapPage } from "./hocs"
import { AdminLayout, AuthLayout, GuestLayout } from "./layouts"
import ErrorPage from "./pages/ErrorPage"


// Routes publiques
const publicRoutes: RouteObject[] = [
  {
    index: true,
    Component: loadPage(import("./pages/home/<USER>")),
  },
  {
    path: PATH_GUEST.root,
    Component: loadPage(import("./pages/home/<USER>")),
  },
]

// Routes d'authentification
const authRoutes: RouteObject[] = [
  {
    index: true,
    element: React.createElement(Navigate, {to: PATH_AUTH.signin}),
  },
  {
    path: SIGNIN,
    Component: loadPage(import("./pages/authentication/SignInPage")),
  },
  {
    path: REQUEST,
    Component: loadPage(import("./pages/authentication/RequestPage")),
  },
  {
    path: UPDATE_PASSWORD,
    Component: loadPage(import("./pages/authentication/UpdatePasswordPage")),
  },
]

// Routes protégées (admin)
const protectedRoutes: RouteObject[] = [

  {
    index:true,
    Component: loadPage(import("./pages/home/<USER>"), {withFooter: true}, true),
  },
  {
    path: PATH_ADMIN.convoyage,
    Component: loadPage(import("./pages/convoyage/ConvoyageAdminPage"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.utilisateurs,
    Component: loadPage(import("./pages/utilisateurs/UserPage"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.agents,
    Component: loadPage(import("./pages/agents"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.primes,
    children: [
      {
        index: true,
        Component: loadPage(import("./pages/primes"), undefined, true),
      },
      {
        path: "add/repartition",
        Component: loadPage(import("./pages/primes/add/AddRepartitionPage"), undefined, true),
      },
      {
        path: "edit/:id",
        Component: loadPage(import("./pages/primes/edit/[id]"), undefined, true),
      },
      {
        path: "repartition/:id",
        Component: loadPage(import("./pages/primes/repartition/RepartitionsPage"), undefined, true),
      }
    ],
  },
]

// Création du routeur
const router = createBrowserRouter(
  [
    {
      ErrorBoundary: ErrorPage,
      children: [
        {
          Component: GuestLayout,
          children: [
            ...publicRoutes,
          ]
        },
        {
          path: PATH_AUTH.root,
          Component: wrapPage(AuthLayout),
          children: [
            ...authRoutes,
          ]
        },
        {
          path: PATH_ADMIN.root,
          Component: wrapPage(AdminLayout),
          children: [
            {
              ErrorBoundary: ErrorPage,
              children: [
                ...protectedRoutes,
              ]
            },
          ]
        },
        {
          path: PATH_GUEST.login,
          element: React.createElement(Navigate, {to: PATH_AUTH.signin}),
        },
        {// Routes d'erreurs
          path: '/:id',
          Component: ErrorPage
        },
      ],
    }
  ],
  { basename: BASENAME }
)

export default router
