import {
  BranchesOutlined,
  IdcardOutlined,
  InfoCircleOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  BookOutlined,
  DashboardOutlined,
  AppstoreAddOutlined,
  BugOutlined,
  GithubOutlined,
  ProductOutlined,
  SecurityScanOutlined,
  SnippetsOutlined,
  UserOutlined,
} from "@ant-design/icons"
import IdentityManager from "./services/IdentityManager"
import React from "react"
import { PATH_ADMIN } from "./constants"

export interface Menus {
  key: string
  label: {
    fr: string
    en: string
  }
  icon?: any
  type?: "group"
  path?: string
  hasChildren: boolean
  children?: Menus[]
  parents?: string[]
}

const getItem = (
  key: string,
  label: string[],
  path?: string | null,
  iconComponent?: any,
  children?: Menus[]
): Menus => {
  const isGroup = key? false : true
  const haveChild = !!children
  return {
    key: !isGroup ? key.trim() + "-" + IdentityManager.fetchUUID() : IdentityManager.fetchUUID(),
    icon: iconComponent? React.createElement(iconComponent, null): null,
    label: { fr: label?.[0], en: label?.[1] ?? label?.[0] },
    type: isGroup ? "group" : undefined,
    path: !haveChild && !isGroup ? path : undefined,
    children: haveChild ? children : undefined,
    hasChildren: haveChild,
  } as Menus
}

export const menuItems = [
  getItem("dashboards", ["Tableau de bord", "Dashboards"], null, PieChartOutlined, [
    getItem("dashboards.cw", ["Custums webb"], PATH_ADMIN.root),
    getItem("projects", ["Sydonia world"], PATH_ADMIN.root),
    getItem("ecommerce", ["Sydonia ++"], PATH_ADMIN.root),
  ]),

  getItem("", ["Pages"]),

  getItem("primes", ["Primes"], PATH_ADMIN.primes, IdcardOutlined),
  getItem("agents", ["Agents"], PATH_ADMIN.agents, IdcardOutlined),
  getItem("convoyages", ["Convoyage"], PATH_ADMIN.convoyage, IdcardOutlined),
  getItem("utilisateurs", ["Utilisateurs"], PATH_ADMIN.utilisateurs, IdcardOutlined),

]
