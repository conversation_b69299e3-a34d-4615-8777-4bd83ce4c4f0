function path(sublink: string, root: string = "/") {
  return root === "/" ? `/${sublink}` : `${root}/${sublink}`
}

const ROOTS_HOME = "/home"
const ROOTS_AUTH = "/auth"
const ROOTS_ADMIN = "/admin"

export const SIGNIN = "signin"
export const REQUEST = "request"
export const UPDATE_PASSWORD = "update-password"
export const SERVICES = "services"
export const LOGIN = "login"
export const DASHBOARD = "dashboard"
export const AGENTS = "agents"
export const CONVOYAGE = "convoyage"
export const UTILISATEURS = "utilisateurs"
export const PRIMES = "primes"

export const PATH_GUEST = {
  root: ROOTS_HOME,
  login: path(LOGIN),
  services: path(SERVICES),
}

export const PATH_AUTH = {
  root: ROOTS_AUTH,
  signin: path(SIGNIN, ROOTS_AUTH),
  request: path(REQUEST, ROOTS_AUTH),
  updatePassword: path(UPDATE_PASSWORD, ROOTS_AUTH),
}

export const PATH_ADMIN = {
  root: ROOTS_ADMIN,
  admin: path(DASHBOARD, ROOTS_ADMIN),
  agents: path(AGENTS, ROOTS_ADMIN),
  convoyage: path(CONVOYAGE, ROOTS_ADMIN),
  utilisateurs: path(UTILISATEURS, ROOTS_ADMIN),
  primes: path(PRIMES, ROOTS_ADMIN),
}

export const PATH_LINKS = {
  customsWebb: "https://cw.douanes.bj/",
  guce: "https://guce.gouv.bj/",
  gouv: "https://www.gouv.bj/",
  finance: "https://finances.bj/",
  omd: "http://www.wcoomd.org/fr.aspx",
  omc: "https://www.wto.org/",
  ecowas: "https://www.ecowas.int/?lang=fr",
  bc: "https://www.benincontrol.com/",
  map: "https://goo.gl/maps/bCPg6bWbZ1KMWQoc8",
}
