import { Fonction, CoefficientConfig } from '../../models/Agent';
import { PrimeType } from '../../models/Prime';
import { fonctions, coefficientsConfig } from '../../mocks/fixtures/fonctions';

export const getFonctions = async (): Promise<Fonction[]> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // const response = await request.get('/api/fonctions');
    // return response.data;

    // Pour le moment, on utilise les données mockées
    return Promise.resolve(fonctions);
  } catch (error) {
    console.error('Error fetching fonctions:', error);
    throw error;
  }
};

export const getCoefficientByFonctionAndPrimeType = (fonction: string, primeType: PrimeType): number => {
  const config = coefficientsConfig.find(
    c => c.fonction === fonction && c.primeType === primeType
  );
  return config?.coefficient || 1.0;
};

export const getAllCoefficientsConfig = (): CoefficientConfig[] => {
  return coefficientsConfig;
};
