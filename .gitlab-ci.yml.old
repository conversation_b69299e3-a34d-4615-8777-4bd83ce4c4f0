stages:
  - dockerize
  - deploy

variables:
  DOCKER_IMAGE: "$CI_REGISTRY_IMAGE/app:latest"

# Job 2: Construction et push de l'image Docker
dockerize_and_push:
  stage: dockerize
  tags:
    - docker-in-docker
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - echo "Dockeriring..."
    - docker --version
    - docker ps
    - echo "$CI_REGISTRY"
    - echo "$CI_REGISTRY_USER"
    - echo "$CI_REGISTRY_IMAGE"
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
    - docker build -t $DOCKER_IMAGE .
    - docker push $DOCKER_IMAGE
  only:
    - main

# Job 3: Déploiement sur le serveur distant (host)
deploy_prod:
  stage: deploy
  tags:
    - docker-socket
  # before_script:
  #   - apk add --no-cache openssh-client
  script:
    - echo "Deploying..."
    - docker --version
    - docker ps
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
    - docker pull $DOCKER_IMAGE
    - docker images
    # - docker stop erp_frontend || true
    # - docker rm app_frontend || true
    # - docker run -d -p 80:80 --name app_frontend $DOCKER_IMAGE
  only:
    - main
  environment:
    name: production