import { useEffect, useState } from "react";
import { PageContainer } from "@ant-design/pro-components";
import { Button, Typography, Table, Space, Tag, Modal, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { Prime, PrimeStatus, PrimeType } from "../../models/Prime";
import { deletePrime, getPrimes } from "../../services/api/primeService";
import { useNavigate } from "react-router";

const PrimesList = () => {
  const [primes, setPrimes] = useState<Prime[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const navigate = useNavigate();

  const fetchPrimes = async () => {
    setLoading(true);
    try {
      const response = await getPrimes();
      setPrimes(response.data);
      setTotal(response.total);
    } catch (error) {
      message.error("Erreur lors du chargement des primes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrimes();
  }, []);

  const handleAdd = () => {
    navigate("/admin/primes/add/repartition");
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/primes/edit/${id}`);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "Êtes-vous sûr de vouloir supprimer cette prime ?",
      content: "Cette action est irréversible.",
      okText: "Oui",
      okType: "danger",
      cancelText: "Non",
      onOk: async () => {
        try {
          await deletePrime(id);
          message.success("Prime supprimée avec succès");
          fetchPrimes();
        } catch (error) {
          message.error("Erreur lors de la suppression de la prime");
        }
      },
    });
  };

  const getPrimeTypeLabel = (type: PrimeType) => {
    const labels = {
      [PrimeType.ANNUAL]: "Annuelle",
      [PrimeType.PERFORMANCE]: "Performance",
      [PrimeType.PROJECT]: "Projet",
      [PrimeType.EXCEPTIONAL]: "Exceptionnelle",
      [PrimeType.OTHER]: "Autre",
    };
    return labels[type] || type;
  };

  const getPrimeStatusColor = (status: PrimeStatus) => {
    const colors = {
      [PrimeStatus.DRAFT]: "default",
      [PrimeStatus.PENDING]: "processing",
      [PrimeStatus.APPROVED]: "success",
      [PrimeStatus.REJECTED]: "error",
    };
    return colors[status] || "default";
  };

  const getPrimeStatusLabel = (status: PrimeStatus) => {
    const labels = {
      [PrimeStatus.DRAFT]: "Brouillon",
      [PrimeStatus.PENDING]: "En attente",
      [PrimeStatus.APPROVED]: "Approuvée",
      [PrimeStatus.REJECTED]: "Rejetée",
    };
    return labels[status] || status;
  };

  const columns = [
    {
      title: "Nom",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (type: PrimeType) => getPrimeTypeLabel(type),
    },
    {
      title: "Montant",
      dataIndex: "amount",
      key: "amount",
      render: (amount: number) => `${amount.toLocaleString()} €`,
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: "Statut",
      dataIndex: "status",
      key: "status",
      render: (status: PrimeStatus) => (
        <Tag color={getPrimeStatusColor(status)}>
          {getPrimeStatusLabel(status)}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: Prime) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record.id)}>
            Modifier
          </Button>
          <Button
            type="link"
            danger
            onClick={() => handleDelete(record.id)}
          >
            Supprimer
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: <Typography.Title level={3} className="w-full">Gestion des Primes</Typography.Title>,
        extra: (
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            Ajouter une répartition
          </Button>
        )
      }}
    >
      <Table
        dataSource={primes}
        columns={columns}
        rowKey="id"
        loading={loading}
        pagination={{
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} primes`,
        }}
      />
    </PageContainer>
  );
};

export default PrimesList;
